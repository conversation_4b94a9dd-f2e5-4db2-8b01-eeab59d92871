'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Star, Download, Eye, ArrowRight, Loader2 } from 'lucide-react';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Template } from '@/types';

// Mock data for templates
const allTemplates = [
  {
    id: '1',
    title: 'Modern Dashboard Pro',
    description: 'Clean and modern dashboard template with dark mode support and advanced analytics',
    category: 'Dashboard',
    price: 49,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.9,
    downloads: 1234,
    featured: true,
    tags: ['React', 'TypeScript', 'Tailwind', 'Charts']
  },
  {
    id: '2',
    title: 'E-commerce Store Complete',
    description: 'Full-featured e-commerce solution with shopping cart, checkout, and admin panel',
    category: 'E-commerce',
    price: 79,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.8,
    downloads: 856,
    featured: true,
    tags: ['Next.js', 'Stripe', 'Responsive', 'SEO']
  },
  {
    id: '3',
    title: 'Landing Page Pro',
    description: 'High-converting landing page template perfect for SaaS products and startups',
    category: 'Landing Page',
    price: 39,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.9,
    downloads: 2341,
    featured: true,
    tags: ['HTML', 'CSS', 'JavaScript', 'Conversion']
  },
  {
    id: '4',
    title: 'Creative Portfolio',
    description: 'Stunning portfolio template for designers, developers, and creative professionals',
    category: 'Portfolio',
    price: 29,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.7,
    downloads: 1567,
    featured: false,
    tags: ['Vue.js', 'GSAP', 'Responsive', 'Animation']
  },
  {
    id: '5',
    title: 'Corporate Website',
    description: 'Professional corporate website template with multiple pages and contact forms',
    category: 'Corporate',
    price: 59,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.6,
    downloads: 892,
    featured: false,
    tags: ['WordPress', 'PHP', 'MySQL', 'CMS']
  },
  {
    id: '6',
    title: 'Mobile App UI Kit',
    description: 'Complete mobile app UI kit with 50+ screens and components for iOS and Android',
    category: 'Mobile App',
    price: 69,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.8,
    downloads: 743,
    featured: false,
    tags: ['React Native', 'Flutter', 'Figma', 'Mobile']
  }
];

const categories = ['All', 'Dashboard', 'E-commerce', 'Landing Page', 'Portfolio', 'Corporate', 'Mobile App'];
const sortOptions = ['Newest', 'Popular', 'Price: Low to High', 'Price: High to Low', 'Rating'];

export default function TemplatesPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('Newest');
  const [templates, setTemplates] = useState<Template[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch templates from Firestore
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const templatesQuery = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));
        const querySnapshot = await getDocs(templatesQuery);
        const templatesData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Template[];

        setTemplates(templatesData);
        setFilteredTemplates(templatesData);
      } catch (error) {
        console.error('Error fetching templates:', error);
        // Fallback to mock data if Firestore fails
        setTemplates(allTemplates);
        setFilteredTemplates(allTemplates);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  // Filter and sort templates
  useEffect(() => {
    let filtered = templates;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Sort templates
    switch (sortBy) {
      case 'Popular':
        filtered.sort((a, b) => b.downloads - a.downloads);
        break;
      case 'Price: Low to High':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'Price: High to Low':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'Rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      default:
        // Newest (featured first, then by id)
        filtered.sort((a, b) => {
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return parseInt(b.id) - parseInt(a.id);
        });
    }

    setFilteredTemplates(filtered);
  }, [searchQuery, selectedCategory, sortBy]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Premium Templates
        </h1>
        <p className="text-xl text-gray-600">
          Discover professionally designed templates for your next project
        </p>
      </div>

      {/* Filters */}
      <div className="mb-8 space-y-4">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Search templates, categories, or tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Filter */}
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Sort */}
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Results count */}
        <div className="text-gray-600">
          Showing {filteredTemplates.length} of {templates.length} templates
        </div>
      </div>

      {/* Loading State */}
      {loading ? (
        <div className="flex items-center justify-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading templates...</span>
        </div>
      ) : (
        <>
          {/* Templates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredTemplates.map((template) => (
          <Card key={template.id} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
            <div className="relative overflow-hidden rounded-t-lg">
              <Image
                src={template.imageUrl}
                alt={template.title}
                width={400}
                height={300}
                className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
                  <Button size="sm" variant="secondary">
                    <Eye className="h-4 w-4 mr-1" />
                    Preview
                  </Button>
                </div>
              </div>
              {template.featured && (
                <Badge className="absolute top-3 left-3 bg-blue-600">
                  Featured
                </Badge>
              )}
            </div>
            
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <Badge variant="outline">{template.category}</Badge>
                <div className="flex items-center text-sm text-gray-600">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                  {(template as any).rating || '4.8'}
                </div>
              </div>
              
              <h3 className="font-semibold text-lg text-gray-900 mb-2">
                {template.title}
              </h3>
              
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                {template.description}
              </p>
              
              <div className="flex flex-wrap gap-1 mb-4">
                {template.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {template.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{template.tags.length - 3}
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm text-gray-600">
                  <Download className="h-4 w-4 mr-1" />
                  {((template as any).downloads || Math.floor(Math.random() * 1000) + 100).toLocaleString()}
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  ${template.price}
                </div>
              </div>
            </CardContent>
            
            <CardFooter className="p-6 pt-0">
              <Button asChild className="w-full">
                <Link href={`/templates/${template.id}`}>
                  View Details
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* No results */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-16">
          <div className="text-gray-400 mb-4">
            <Filter className="h-16 w-16 mx-auto" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No templates found</h3>
          <p className="text-gray-600 mb-6">
            Try adjusting your search criteria or browse all templates
          </p>
          <Button onClick={() => {
            setSearchQuery('');
            setSelectedCategory('All');
            setSortBy('Newest');
          }}>
            Clear Filters
          </Button>
        </div>
      )}
        </>
      )}
    </div>
  );
}
