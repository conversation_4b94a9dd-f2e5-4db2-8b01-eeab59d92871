'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Filter, Star, Download, Eye, Loader2, Heart, ShoppingCart, FileText } from 'lucide-react';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Template } from '@/types';

// Mock data for templates
const allTemplates = [
  {
    id: '1',
    title: 'Creative Portfolio',
    description: 'Showcase your creative work with this stunning portfolio template',
    category: 'Portfolio',
    price: 1999,
    originalPrice: 2999,
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
    rating: 4.9,
    downloads: 1234,
    featured: true,
    tags: ['Responsive', 'Modern', 'Fast'],
    discount: 33,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    id: '2',
    title: 'E-commerce Store',
    description: 'Complete e-commerce solution with shopping cart and payment integration',
    category: 'E-commerce',
    price: 4999,
    originalPrice: 7499,
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',
    rating: 4.9,
    downloads: 1234,
    featured: true,
    tags: ['Responsive', 'Modern', 'Fast'],
    discount: 33,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    id: '3',
    title: 'Education Platform',
    description: 'Complete education platform template with course management',
    category: 'Education',
    price: 3499,
    originalPrice: 5249,
    imageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',
    rating: 4.9,
    downloads: 1234,
    featured: true,
    tags: ['Responsive', 'Modern', 'Fast'],
    discount: 33,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    id: '4',
    title: 'Technology Dashboard',
    description: 'Modern dashboard template for technology companies',
    category: 'Technology',
    price: 2999,
    originalPrice: 4499,
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',
    rating: 4.8,
    downloads: 987,
    featured: false,
    tags: ['Responsive', 'Modern', 'Fast'],
    discount: 33,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    id: '5',
    title: 'Business Corporate',
    description: 'Professional corporate website template',
    category: 'Business',
    price: 3999,
    originalPrice: 5999,
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
    rating: 4.7,
    downloads: 756,
    featured: false,
    tags: ['Responsive', 'Modern', 'Fast'],
    discount: 33,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    id: '6',
    title: 'Restaurant Website',
    description: 'Beautiful restaurant website with menu and reservation system',
    category: 'Restaurant',
    price: 2499,
    originalPrice: 3749,
    imageUrl: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop',
    rating: 4.6,
    downloads: 543,
    featured: false,
    tags: ['Responsive', 'Modern', 'Fast'],
    discount: 33,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  }
];

const categories = [
  { name: 'All', count: 7 },
  { name: 'Technology', count: 1 },
  { name: 'Business', count: 2 },
  { name: 'Education', count: 1 },
  { name: 'Portfolio', count: 1 },
  { name: 'E-commerce', count: 1 },
  { name: 'Restaurant', count: 1 }
];
export default function TemplatesPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('Newest');
  const [templates, setTemplates] = useState<Template[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch templates from Firestore
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const templatesQuery = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));
        const querySnapshot = await getDocs(templatesQuery);
        const templatesData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Template[];

        setTemplates(templatesData);
        setFilteredTemplates(templatesData);
      } catch (error) {
        console.error('Error fetching templates:', error);
        // Fallback to mock data if Firestore fails
        setTemplates(allTemplates);
        setFilteredTemplates(allTemplates);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  // Filter and sort templates
  useEffect(() => {
    let filtered = templates;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Sort templates
    switch (sortBy) {
      case 'Popular':
        filtered.sort((a, b) => (b.downloads || 0) - (a.downloads || 0));
        break;
      case 'Price: Low to High':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'Price: High to Low':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'Rating':
        filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      default:
        // Newest (featured first, then by id)
        filtered.sort((a, b) => {
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return parseInt(b.id) - parseInt(a.id);
        });
    }

    setFilteredTemplates(filtered);
  }, [searchQuery, selectedCategory, sortBy]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        {/* Categories Header */}
        <div className="mb-6 sm:mb-8">
          <h2 className="text-lg sm:text-xl font-medium text-gray-900 mb-4">Categories</h2>

          {/* Category Pills */}
          <div className="flex flex-wrap gap-2 sm:gap-3 mb-4 sm:mb-6">
            {categories.map((category) => (
              <button
                key={category.name}
                onClick={() => setSelectedCategory(category.name)}
                className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium transition-colors ${
                  selectedCategory === category.name
                    ? 'bg-gray-900 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                }`}
              >
                {category.name}
                {category.name !== 'All' && (
                  <span className="ml-1 sm:ml-2 text-xs opacity-70">{category.count}</span>
                )}
              </button>
            ))}
          </div>

          {/* Results count */}
          <div className="text-xs sm:text-sm text-gray-600">
            Showing {filteredTemplates.length} of {templates.length} templates
          </div>
        </div>

        {/* Loading State */}
        {loading ? (
          <div className="flex items-center justify-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading templates...</span>
          </div>
        ) : (
          <>
            {/* Templates Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {filteredTemplates.map((template) => (
                <div key={template.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                  {/* Template Image */}
                  <div className="relative group">
                    <div className="aspect-[4/3] bg-gray-100 flex items-center justify-center relative overflow-hidden">
                      {template.imageUrl.includes('unsplash') ? (
                        <img
                          src={template.imageUrl}
                          alt={template.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="flex flex-col items-center justify-center text-gray-400">
                          <div className="w-12 h-12 mb-2 flex items-center justify-center">
                            <FileText className="w-8 h-8" />
                          </div>
                          <span className="text-sm font-medium">Template Preview</span>
                        </div>
                      )}
                    </div>

                    {/* Category Badge */}
                    <div className="absolute top-2 sm:top-3 left-2 sm:left-3">
                      <span className="px-2 py-1 bg-white text-gray-700 text-xs font-medium rounded">
                        {template.category}
                      </span>
                    </div>

                    {/* Favorite Icon */}
                    <button className="absolute top-2 sm:top-3 right-2 sm:right-3 p-1 sm:p-1.5 bg-white rounded-full shadow-sm hover:bg-gray-50">
                      <Heart className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />
                    </button>
                  </div>

                  {/* Template Info */}
                  <div className="p-3 sm:p-4">
                    <h3 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">{template.title}</h3>
                    <p className="text-xs sm:text-sm text-gray-600 mb-3 line-clamp-2">{template.description}</p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1 mb-3 sm:mb-4">
                      {template.tags.map((tag) => (
                        <span key={tag} className="px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* Price and Rating */}
                    <div className="flex items-center justify-between mb-3 sm:mb-4">
                      <div className="flex items-center space-x-1 sm:space-x-2">
                        <span className="text-base sm:text-lg font-bold text-green-600">₹{template.price}</span>
                        {template.originalPrice && (
                          <>
                            <span className="text-xs sm:text-sm text-gray-400 line-through">₹{template.originalPrice}</span>
                            <span className="text-xs text-green-600 font-medium">{template.discount}% OFF</span>
                          </>
                        )}
                      </div>
                      <div className="flex items-center text-xs text-gray-500">
                        <Star className="w-3 h-3 fill-yellow-400 text-yellow-400 mr-1" />
                        {template.rating}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="flex-1 text-xs sm:text-sm">
                        <Eye className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        <span className="hidden sm:inline">Preview</span>
                        <span className="sm:hidden">View</span>
                      </Button>
                      <Button size="sm" className="flex-1 bg-blue-600 hover:bg-blue-700 text-xs sm:text-sm">
                        <ShoppingCart className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        <span className="hidden sm:inline">Buy Now</span>
                        <span className="sm:hidden">Buy</span>
                      </Button>
                    </div>

                    {/* Downloads */}
                    <div className="flex items-center justify-between mt-2 sm:mt-3 pt-2 sm:pt-3 border-t border-gray-100">
                      <div className="flex items-center text-xs text-gray-500">
                        <Download className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">{template.downloads?.toLocaleString()} downloads</span>
                        <span className="sm:hidden">{(template.downloads || 0) > 1000 ? `${Math.floor((template.downloads || 0) / 1000)}k` : template.downloads}</span>
                      </div>
                      <span className="text-xs text-gray-500 hidden sm:inline">Updated recently</span>
                      <span className="text-xs text-gray-500 sm:hidden">Recent</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* No results */}
            {filteredTemplates.length === 0 && (
              <div className="text-center py-16">
                <div className="text-gray-400 mb-4">
                  <Filter className="h-16 w-16 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No templates found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search criteria or browse all templates
                </p>
                <Button onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('All');
                  setSortBy('Newest');
                }}>
                  Clear Filters
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
