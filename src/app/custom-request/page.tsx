'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Loader2,
  CheckCircle,
  Clock,
  DollarSign,
  FileText,
  Lightbulb,
  Users,
  Zap,
  Palette,
  Rocket,
  Award,
  BookOpen
} from 'lucide-react';
import Link from 'next/link';
import { createCustomRequest } from '@/lib/firebaseServices';

const categories = [
  'Dashboard',
  'E-commerce',
  'Landing Page',
  'Portfolio',
  'Corporate',
  'Mobile App',
  'Blog/CMS',
  'Other'
];

const features = [
  {
    icon: Palette,
    title: 'Custom Design',
    description: 'Tailored specifically to your brand and requirements'
  },
  {
    icon: Rocket,
    title: 'Fast Delivery',
    description: 'Most projects completed within 5-10 business days'
  },
  {
    icon: Users,
    title: 'Expert Team',
    description: 'Experienced designers and developers working on your project'
  },
  {
    icon: BookOpen,
    title: 'Full Documentation',
    description: 'Complete setup guide and documentation included'
  }
];

export default function CustomRequestPage() {
  const { user, userData } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    budget: '',
    deadline: '',
    features: '',
    inspiration: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
    setError('');
  };

  const handleCategoryChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      category: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validation
    if (!formData.title || !formData.description || !formData.category) {
      setError('Please fill in all required fields');
      setIsLoading(false);
      return;
    }

    try {
      // Create custom request in Firestore
      await createCustomRequest({
        userId: user.uid,
        userEmail: user.email!,
        title: formData.title,
        description: formData.description,
        category: formData.category,
        budget: formData.budget ? parseFloat(formData.budget.replace(/[^0-9.-]+/g, '')) : undefined,
        deadline: formData.deadline ? new Date(formData.deadline) : undefined,
        status: 'pending'
      });

      setIsSubmitted(true);
    } catch (error: any) {
      setError(error.message || 'Failed to submit request');
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Sign in Required</h1>
        <p className="text-gray-600 mb-6">Please sign in to submit a custom design request.</p>
        <Button asChild>
          <Link href="/auth">Sign In</Link>
        </Button>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className="container mx-auto px-4 py-20">
        <Card className="max-w-2xl mx-auto text-center">
          <CardContent className="p-8">
            <div className="mb-6">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Request Submitted!</h1>
              <p className="text-gray-600">
                Thank you for your custom design request. Our team will review it and get back to you within 24 hours.
              </p>
            </div>
            
            <div className="space-y-4">
              <Button asChild className="w-full">
                <Link href="/dashboard">Go to Dashboard</Link>
              </Button>
              <Button asChild variant="outline" className="w-full">
                <Link href="/templates">Browse Templates</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Request Custom Design
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Can't find what you're looking for? Let our expert team create a custom template tailored to your specific needs.
        </p>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
              <CardDescription>
                Tell us about your project requirements and we'll create something amazing for you.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Project Title *</Label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="e.g., Modern SaaS Dashboard"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category} onValueChange={handleCategoryChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Project Description *</Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Describe your project in detail. What is the purpose? Who is the target audience? What features do you need?"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="features">Required Features</Label>
                  <Textarea
                    id="features"
                    name="features"
                    placeholder="List specific features you need (e.g., user authentication, payment integration, responsive design, etc.)"
                    value={formData.features}
                    onChange={handleInputChange}
                    rows={3}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="budget">Budget Range (USD)</Label>
                    <Input
                      id="budget"
                      name="budget"
                      placeholder="e.g., $500 - $1000"
                      value={formData.budget}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="deadline">Preferred Deadline</Label>
                    <Input
                      id="deadline"
                      name="deadline"
                      type="date"
                      value={formData.deadline}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="inspiration">Inspiration/References</Label>
                  <Textarea
                    id="inspiration"
                    name="inspiration"
                    placeholder="Share any websites, designs, or references that inspire you. Include URLs if possible."
                    value={formData.inspiration}
                    onChange={handleInputChange}
                    rows={3}
                  />
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Submit Request
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Features */}
          <Card>
            <CardHeader>
              <CardTitle>Why Choose Custom Design?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <IconComponent className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{feature.title}</h4>
                      <p className="text-sm text-gray-600">{feature.description}</p>
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>

          {/* Process */}
          <Card>
            <CardHeader>
              <CardTitle>Our Process</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Badge className="bg-blue-100 text-blue-800">1</Badge>
                <div>
                  <p className="font-medium">Submit Request</p>
                  <p className="text-sm text-gray-600">Fill out the form with your requirements</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Badge className="bg-blue-100 text-blue-800">2</Badge>
                <div>
                  <p className="font-medium">Review & Quote</p>
                  <p className="text-sm text-gray-600">We'll review and send you a detailed quote</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Badge className="bg-blue-100 text-blue-800">3</Badge>
                <div>
                  <p className="font-medium">Design & Develop</p>
                  <p className="text-sm text-gray-600">Our team creates your custom template</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Badge className="bg-blue-100 text-blue-800">4</Badge>
                <div>
                  <p className="font-medium">Delivery</p>
                  <p className="text-sm text-gray-600">Receive your completed template with documentation</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact */}
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Have questions about custom design? Our team is here to help.
              </p>
              <Button asChild variant="outline" className="w-full">
                <Link href="/contact">Contact Us</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
