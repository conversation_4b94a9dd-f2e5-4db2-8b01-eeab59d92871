import { collection, addDoc, doc, setDoc } from 'firebase/firestore';
import { db } from './firebase';

// Sample templates data
export const sampleTemplates = [
  {
    title: 'Modern Dashboard',
    description: 'Clean and modern dashboard template with analytics and data visualization',
    category: 'Dashboard',
    price: 49,
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['React', 'TypeScript', 'Charts', 'Analytics'],
    featured: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'E-commerce Store',
    description: 'Complete e-commerce solution with shopping cart and payment integration',
    category: 'E-commerce',
    price: 79,
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['Next.js', 'Stripe', 'Shopping Cart', 'Responsive'],
    featured: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'Landing Page Pro',
    description: 'High-converting landing page template for SaaS and startups',
    category: 'Landing Page',
    price: 39,
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['HTML', 'CSS', 'JavaScript', 'Conversion'],
    featured: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'Portfolio Showcase',
    description: 'Creative portfolio template for designers and developers',
    category: 'Portfolio',
    price: 29,
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['Vue.js', 'GSAP', 'Animation', 'Creative'],
    featured: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'Corporate Website',
    description: 'Professional corporate website template with multiple pages',
    category: 'Corporate',
    price: 59,
    imageUrl: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['WordPress', 'PHP', 'Corporate', 'Professional'],
    featured: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'Mobile App UI',
    description: 'Complete mobile app UI kit with 50+ screens',
    category: 'Mobile App',
    price: 69,
    imageUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['React Native', 'Flutter', 'Mobile', 'UI Kit'],
    featured: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  }
];

// Sample categories data
export const sampleCategories = [
  {
    name: 'Dashboard',
    description: 'Admin panels and data visualization templates',
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'E-commerce',
    description: 'Online stores and shopping cart templates',
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'Landing Page',
    description: 'High-converting marketing pages',
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'Portfolio',
    description: 'Creative showcases for professionals',
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'Corporate',
    description: 'Business and company websites',
    imageUrl: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'Mobile App',
    description: 'Mobile application UI templates',
    imageUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',
    templateCount: 0
  }
];

// Function to add sample data to Firestore
export const addSampleData = async () => {
  try {
    console.log('Adding sample templates...');
    
    // Add templates
    for (const template of sampleTemplates) {
      await addDoc(collection(db, 'templates'), template);
    }
    
    console.log('Adding sample categories...');
    
    // Add categories
    for (const category of sampleCategories) {
      await addDoc(collection(db, 'categories'), category);
    }
    
    console.log('Sample data added successfully!');
  } catch (error) {
    console.error('Error adding sample data:', error);
  }
};

// Function to create an admin user (call this after signing up)
export const makeUserAdmin = async (userId: string) => {
  try {
    await setDoc(doc(db, 'users', userId), {
      role: 'admin'
    }, { merge: true });
    console.log('User made admin successfully!');
  } catch (error) {
    console.error('Error making user admin:', error);
  }
};
