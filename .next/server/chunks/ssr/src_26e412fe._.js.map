{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/templates/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardFooter } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Search, Filter, Star, Download, Eye, ArrowRight, Loader2 } from 'lucide-react';\nimport { collection, getDocs, query, orderBy } from 'firebase/firestore';\nimport { db } from '@/lib/firebase';\nimport { Template } from '@/types';\n\n// Mock data for templates\nconst allTemplates = [\n  {\n    id: '1',\n    title: 'Modern Dashboard Pro',\n    description: 'Clean and modern dashboard template with dark mode support and advanced analytics',\n    category: 'Dashboard',\n    price: 49,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.9,\n    downloads: 1234,\n    featured: true,\n    tags: ['React', 'TypeScript', 'Tailwind', 'Charts']\n  },\n  {\n    id: '2',\n    title: 'E-commerce Store Complete',\n    description: 'Full-featured e-commerce solution with shopping cart, checkout, and admin panel',\n    category: 'E-commerce',\n    price: 79,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.8,\n    downloads: 856,\n    featured: true,\n    tags: ['Next.js', 'Stripe', 'Responsive', 'SEO']\n  },\n  {\n    id: '3',\n    title: 'Landing Page Pro',\n    description: 'High-converting landing page template perfect for SaaS products and startups',\n    category: 'Landing Page',\n    price: 39,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.9,\n    downloads: 2341,\n    featured: true,\n    tags: ['HTML', 'CSS', 'JavaScript', 'Conversion']\n  },\n  {\n    id: '4',\n    title: 'Creative Portfolio',\n    description: 'Stunning portfolio template for designers, developers, and creative professionals',\n    category: 'Portfolio',\n    price: 29,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.7,\n    downloads: 1567,\n    featured: false,\n    tags: ['Vue.js', 'GSAP', 'Responsive', 'Animation']\n  },\n  {\n    id: '5',\n    title: 'Corporate Website',\n    description: 'Professional corporate website template with multiple pages and contact forms',\n    category: 'Corporate',\n    price: 59,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.6,\n    downloads: 892,\n    featured: false,\n    tags: ['WordPress', 'PHP', 'MySQL', 'CMS']\n  },\n  {\n    id: '6',\n    title: 'Mobile App UI Kit',\n    description: 'Complete mobile app UI kit with 50+ screens and components for iOS and Android',\n    category: 'Mobile App',\n    price: 69,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.8,\n    downloads: 743,\n    featured: false,\n    tags: ['React Native', 'Flutter', 'Figma', 'Mobile']\n  }\n];\n\nconst categories = ['All', 'Dashboard', 'E-commerce', 'Landing Page', 'Portfolio', 'Corporate', 'Mobile App'];\nconst sortOptions = ['Newest', 'Popular', 'Price: Low to High', 'Price: High to Low', 'Rating'];\n\nexport default function TemplatesPage() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n  const [sortBy, setSortBy] = useState('Newest');\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  // Fetch templates from Firestore\n  useEffect(() => {\n    const fetchTemplates = async () => {\n      try {\n        const templatesQuery = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));\n        const querySnapshot = await getDocs(templatesQuery);\n        const templatesData = querySnapshot.docs.map(doc => ({\n          id: doc.id,\n          ...doc.data(),\n          createdAt: doc.data().createdAt?.toDate() || new Date(),\n          updatedAt: doc.data().updatedAt?.toDate() || new Date(),\n        })) as Template[];\n\n        setTemplates(templatesData);\n        setFilteredTemplates(templatesData);\n      } catch (error) {\n        console.error('Error fetching templates:', error);\n        // Fallback to mock data if Firestore fails\n        setTemplates(allTemplates);\n        setFilteredTemplates(allTemplates);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTemplates();\n  }, []);\n\n  // Filter and sort templates\n  useEffect(() => {\n    let filtered = templates;\n\n    // Filter by search query\n    if (searchQuery) {\n      filtered = filtered.filter(template =>\n        template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))\n      );\n    }\n\n    // Filter by category\n    if (selectedCategory !== 'All') {\n      filtered = filtered.filter(template => template.category === selectedCategory);\n    }\n\n    // Sort templates\n    switch (sortBy) {\n      case 'Popular':\n        filtered.sort((a, b) => b.downloads - a.downloads);\n        break;\n      case 'Price: Low to High':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'Price: High to Low':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'Rating':\n        filtered.sort((a, b) => b.rating - a.rating);\n        break;\n      default:\n        // Newest (featured first, then by id)\n        filtered.sort((a, b) => {\n          if (a.featured && !b.featured) return -1;\n          if (!a.featured && b.featured) return 1;\n          return parseInt(b.id) - parseInt(a.id);\n        });\n    }\n\n    setFilteredTemplates(filtered);\n  }, [searchQuery, selectedCategory, sortBy]);\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n          Premium Templates\n        </h1>\n        <p className=\"text-xl text-gray-600\">\n          Discover professionally designed templates for your next project\n        </p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"mb-8 space-y-4\">\n        <div className=\"flex flex-col lg:flex-row gap-4\">\n          {/* Search */}\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n            <Input\n              placeholder=\"Search templates, categories, or tags...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n\n          {/* Category Filter */}\n          <Select value={selectedCategory} onValueChange={setSelectedCategory}>\n            <SelectTrigger className=\"w-full lg:w-48\">\n              <SelectValue placeholder=\"Category\" />\n            </SelectTrigger>\n            <SelectContent>\n              {categories.map((category) => (\n                <SelectItem key={category} value={category}>\n                  {category}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n\n          {/* Sort */}\n          <Select value={sortBy} onValueChange={setSortBy}>\n            <SelectTrigger className=\"w-full lg:w-48\">\n              <SelectValue placeholder=\"Sort by\" />\n            </SelectTrigger>\n            <SelectContent>\n              {sortOptions.map((option) => (\n                <SelectItem key={option} value={option}>\n                  {option}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        </div>\n\n        {/* Results count */}\n        <div className=\"text-gray-600\">\n          Showing {filteredTemplates.length} of {templates.length} templates\n        </div>\n      </div>\n\n      {/* Loading State */}\n      {loading ? (\n        <div className=\"flex items-center justify-center py-20\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-blue-600\" />\n          <span className=\"ml-2 text-gray-600\">Loading templates...</span>\n        </div>\n      ) : (\n        <>\n          {/* Templates Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {filteredTemplates.map((template) => (\n          <Card key={template.id} className=\"group hover:shadow-xl transition-all duration-300 border-0 shadow-lg\">\n            <div className=\"relative overflow-hidden rounded-t-lg\">\n              <Image\n                src={template.imageUrl}\n                alt={template.title}\n                width={400}\n                height={300}\n                className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n              />\n              <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center\">\n                <div className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2\">\n                  <Button size=\"sm\" variant=\"secondary\">\n                    <Eye className=\"h-4 w-4 mr-1\" />\n                    Preview\n                  </Button>\n                </div>\n              </div>\n              {template.featured && (\n                <Badge className=\"absolute top-3 left-3 bg-blue-600\">\n                  Featured\n                </Badge>\n              )}\n            </div>\n            \n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <Badge variant=\"outline\">{template.category}</Badge>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <Star className=\"h-4 w-4 fill-yellow-400 text-yellow-400 mr-1\" />\n                  {(template as any).rating || '4.8'}\n                </div>\n              </div>\n              \n              <h3 className=\"font-semibold text-lg text-gray-900 mb-2\">\n                {template.title}\n              </h3>\n              \n              <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n                {template.description}\n              </p>\n              \n              <div className=\"flex flex-wrap gap-1 mb-4\">\n                {template.tags.slice(0, 3).map((tag) => (\n                  <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n                    {tag}\n                  </Badge>\n                ))}\n                {template.tags.length > 3 && (\n                  <Badge variant=\"secondary\" className=\"text-xs\">\n                    +{template.tags.length - 3}\n                  </Badge>\n                )}\n              </div>\n              \n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <Download className=\"h-4 w-4 mr-1\" />\n                  {((template as any).downloads || Math.floor(Math.random() * 1000) + 100).toLocaleString()}\n                </div>\n                <div className=\"text-2xl font-bold text-gray-900\">\n                  ${template.price}\n                </div>\n              </div>\n            </CardContent>\n            \n            <CardFooter className=\"p-6 pt-0\">\n              <Button asChild className=\"w-full\">\n                <Link href={`/templates/${template.id}`}>\n                  View Details\n                  <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </Link>\n              </Button>\n            </CardFooter>\n          </Card>\n        ))}\n      </div>\n\n      {/* No results */}\n      {filteredTemplates.length === 0 && (\n        <div className=\"text-center py-16\">\n          <div className=\"text-gray-400 mb-4\">\n            <Filter className=\"h-16 w-16 mx-auto\" />\n          </div>\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No templates found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            Try adjusting your search criteria or browse all templates\n          </p>\n          <Button onClick={() => {\n            setSearchQuery('');\n            setSelectedCategory('All');\n            setSortBy('Newest');\n          }}>\n            Clear Filters\n          </Button>\n        </div>\n      )}\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAZA;;;;;;;;;;;;;AAeA,0BAA0B;AAC1B,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAS;YAAc;YAAY;SAAS;IACrD;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAW;YAAU;YAAc;SAAM;IAClD;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAQ;YAAO;YAAc;SAAa;IACnD;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAU;YAAQ;YAAc;SAAY;IACrD;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAa;YAAO;YAAS;SAAM;IAC5C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAgB;YAAW;YAAS;SAAS;IACtD;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAa;IAAc;IAAgB;IAAa;IAAa;CAAa;AAC7G,MAAM,cAAc;IAAC;IAAU;IAAW;IAAsB;IAAsB;CAAS;AAEhF,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;gBAC/E,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;gBACpC,MAAM,gBAAgB,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBACnD,IAAI,IAAI,EAAE;wBACV,GAAG,IAAI,IAAI,EAAE;wBACb,WAAW,IAAI,IAAI,GAAG,SAAS,EAAE,YAAY,IAAI;wBACjD,WAAW,IAAI,IAAI,GAAG,SAAS,EAAE,YAAY,IAAI;oBACnD,CAAC;gBAED,aAAa;gBACb,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,2CAA2C;gBAC3C,aAAa;gBACb,qBAAqB;YACvB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,yBAAyB;QACzB,IAAI,aAAa;YACf,WAAW,SAAS,MAAM,CAAC,CAAA,WACzB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC7D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACnE,SAAS,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEhF;QAEA,qBAAqB;QACrB,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;QAC/D;QAEA,iBAAiB;QACjB,OAAQ;YACN,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;gBACjD;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;gBACzC;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;gBACzC;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;gBAC3C;YACF;gBACE,sCAAsC;gBACtC,SAAS,IAAI,CAAC,CAAC,GAAG;oBAChB,IAAI,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC;oBACvC,IAAI,CAAC,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,OAAO;oBACtC,OAAO,SAAS,EAAE,EAAE,IAAI,SAAS,EAAE,EAAE;gBACvC;QACJ;QAEA,qBAAqB;IACvB,GAAG;QAAC;QAAa;QAAkB;KAAO;IAE1C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAGlE,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAKd,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAkB,eAAe;;kDAC9C,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;kDACX,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,aAAU;gDAAgB,OAAO;0DAC/B;+CADc;;;;;;;;;;;;;;;;0CAQvB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAQ,eAAe;;kDACpC,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;kDACX,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,kIAAA,CAAA,aAAU;gDAAc,OAAO;0DAC7B;+CADc;;;;;;;;;;;;;;;;;;;;;;kCASzB,8OAAC;wBAAI,WAAU;;4BAAgB;4BACpB,kBAAkB,MAAM;4BAAC;4BAAK,UAAU,MAAM;4BAAC;;;;;;;;;;;;;YAK3D,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;qCAGvC;;kCAEE,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,yBAC1B,8OAAC,gIAAA,CAAA,OAAI;gCAAmB,WAAU;;kDAChC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,SAAS,QAAQ;gDACtB,KAAK,SAAS,KAAK;gDACnB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;;0EACxB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;4CAKrC,SAAS,QAAQ,kBAChB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;;;;;;;kDAMzD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,SAAS,QAAQ;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACd,SAAiB,MAAM,IAAI;;;;;;;;;;;;;0DAIjC,8OAAC;gDAAG,WAAU;0DACX,SAAS,KAAK;;;;;;0DAGjB,8OAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;0DAGvB,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC9B,8OAAC,iIAAA,CAAA,QAAK;4DAAW,SAAQ;4DAAY,WAAU;sEAC5C;2DADS;;;;;oDAIb,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;4DAAU;4DAC3C,SAAS,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;0DAK/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,CAAC,AAAC,SAAiB,SAAS,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,GAAG,EAAE,cAAc;;;;;;;kEAEzF,8OAAC;wDAAI,WAAU;;4DAAmC;4DAC9C,SAAS,KAAK;;;;;;;;;;;;;;;;;;;kDAKtB,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,WAAU;sDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;;oDAAE;kEAEvC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BArEnB,SAAS,EAAE;;;;;;;;;;oBA8EzB,kBAAkB,MAAM,KAAK,mBAC5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;oCACf,eAAe;oCACf,oBAAoB;oCACpB,UAAU;gCACZ;0CAAG;;;;;;;;;;;;;;;;;;;;AASb", "debugId": null}}]}